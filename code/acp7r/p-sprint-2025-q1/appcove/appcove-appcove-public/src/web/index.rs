#[approck::http(GET /; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.add_body(html! {
            about-appcove {
                grid-2 {
                    // Left Column (AppCove Summary)
                    #page-heading {
                        div {
                            // Yellow Circle Background
                            div class="circle-bg" {} 
                            grid-12 {
                                div class="circle-image" {
                                        img src="https://s3.us-east-2.amazonaws.com/asset7.net/AppCove/blue-circle-man.png" max-width="350" height="300" margin-right="10px" margin-bottom="0px" class ="circle-image" alt="AppCove profile" id="float-image" {} }
                                } 
                                h1 { "AppCove" }
                                    p {
                                    "We build custom software focused on providing "
                                    strong { "your customers" }
                                    "with a premium experience."
                                    }
                                
                            
                        
                            #page-body {
                                header {
                                    p {
                                        "AppCove has a proven track record of building and supporting reliable web applications for over 20 years. "
                                        "We take your long term business goals seriously."
                                    }
                                }
                            }
                        
                    
                            #page-body {
                                content {
                                    "This is reflected in every aspect of our work — from our people and training to our software and contracts. "
                                    "While you focus on innovation and operations, we focus on delivering technical excellence, robust security, "
                                    "and flexible infrastructure — whether hosted with us or deployed on your cloud."
                                }
                            }
                        
                            #page-body {
                                h6 { "Company Information" }
                                p {
                                    strong { "FOUNDED" } "2003"
                                    br;
                                    strong { "OWNER" } "Jason Garber"
                                    br;
                                    strong { "SIZE" } "20+ Employees"
                                    br;
                                    strong { "HEADQUARTERS" } "Altoona, PA"
                                    br;
                                }
                            }

                            #page-main {
                                grid-3 {
                                    header-2 { strong { "Problems" } }
                                    br;
                                    header-3 { strong { "Answers" } }
                                }
                                hr;
                                div {
                                    grid-3 {
                                        p { "Business Data is contained in a growing number of SaaS providers that do not communicate well with each other." }
                                        div class="arrow-icon-wrapper" {
                                            i class="fas fa-arrow-right arrow-icon" {}
                                        }
                                        p { "AppCove has an intense focus on well-designed custom databases for each client we work with. Having all key data in one central database allows everything else to fall into place." }
                                    }
                                    hr;
                                    grid-3 {
                                        p { "Premium Customer Experience can be difficult to provide when using a collection of off-the-shelf SaaS tools." }
                                        div class="arrow-icon-wrapper" {
                                            i class="fas fa-arrow-right arrow-icon" {}
                                        }
                                        p { "AppCove provides a unified experience with correct security and access controls covering all stakeholders: owners / administrators / staff / customers / end users." }
                                    }
                                    hr;
                                    grid-3 {
                                        p { "Custom software can be slow to build, with uncertain outcomes, and may lack long term support." }
                                        div class="arrow-icon-wrapper" {
                                            i class="fas fa-arrow-right arrow-icon" {}
                                        }
                                        p { "AppCove’s pursuit of technical excellence, coupled with a willingness to learn the details of your business results in efficient code, streamlined implementation, and a custom software product built for long-term support." }
                                    }
                                    hr;
                                    grid-3 {
                                        p { "Artificial intelligence fails to maximize value if it does not have access to reliable, structured business data" }
                                        div class="arrow-icon-wrapper" {
                                            i class="fas fa-arrow-right arrow-icon" {}
                                        }
                                        p { "AppCove’s rigorous database design approach positions each client ideally to participate in the rapid increase of AI capabilities." }
                                    }
                                }
                            }

                            #page-body {
                                h6 { "Key Staff" }
                                br;
                                grid-2 {
                                    div class="staff-card" {
                                        h4 { "Jason Garber" }
                                        p { "President & Architect" }
                                        years { "22 YEARS WITH APPCOVE" }
                                    }
                                    div class="staff-card" {
                                        h4 { "Julie Garber" }
                                        p { "Director of Operations" }
                                    }
                                    div class="staff-card" {
                                        h4 { "Andrew Bidochko" }
                                        p { "Senior Engineer" }
                                        years { "21 YEARS WITH APPCOVE" }
                                    }
                                    div class="staff-card" {
                                        h4 { "Sergio Olivo" }
                                        p { "Technical Project Consultant" }
                                        years { "19 YEARS WITH APPCOVE" }
                                    }
                                    div class="staff-card" {
                                        h4 { "Jeff Berdin" }
                                        p { "Director of Infrastructure" }
                                    }
                                    div class="staff-card" {
                                        h4 { "Iryna Bidochko" }
                                        p { "Software Engineer" }
                                        years { "10 YEARS WITH APPCOVE" }
                                    }
                                    div class="staff-card" {
                                        h4 { "Jessi Garber" }
                                        p { "Senior UI/UX Developer" }
                                        years { "10 YEARS WITH APPCOVE" }
                                    }
                                    div class="staff-card" {
                                        h4 { "Don Berdin" }
                                        p { "Director of Support" }
                                    }
                                }
                            }

                            #page-footer {
                                footer {
                                    p {strong { "FOR BUSINESS INQUIRIES, CONTACT" }}
                                    grid-2 {
                                        p {
                                            "Jason Garber" br;
                                            "<EMAIL>" br;
                                            "(814) 240-3338"
                                        }
                                        p {
                                            "AppCove, Inc." br;
                                            "P.O. Box 1309" br;
                                            "Altoona PA 16603"
                                        }
                                    }

                                    br;
                                    div class="company-logo" {
                                        img src="https://asset7.net/AppCove/appcove_logo_2.png" {}
                                    }
                                }
                            }
                        }
                    }
                        
                            #page-body {
                                div {
                                    grid-2 {
                                        // RIGHT SIDE (Timeline)
                                        cell-4 #timeline-panel {
                                            h5.section-title { "Custom Software Deployment Timeline" }
                                            p."text-upper text-bold text-small p-notable" { "A SELECTION OF NOTABLE PROJECTS" }
                                            br;
                                            div.timeline-container {
                                                div.timeline-item {
                                                    span.timeline-dot {}
                                                    span.timeline-year { "2004 — 21 Years" }
                                                    span.active-project { " (Active Project)" } br;
                                                    strong { "Advanced Reservation System" } br;
                                                    small { "Connected staff, guides, clients, guests, housekeeping together in one unified scheduling portal for an exclusive Pennsylvania Fishing club." }
                                                }
                                                div.timeline-item {
                                                    span.timeline-dot {}
                                                    span.timeline-year { "2005 — 15 Years" }
                                                    span.inactive-project { " (Inactive Project)" } br;
                                                    strong { "CRM & Marketing Software" } br;
                                                    small { "Constructed a database system to collect and distribute millions of leads to thousands of small business owners nationwide via a unique filtering engine and CRM." }
                                                }
                                                div.timeline-item {
                                                    span.timeline-dot {}
                                                    span.timeline-year { "2009 — 16 Years" }
                                                    span.active-project { " (Active Project)" } br;
                                                    strong { "Client Marketing Dashboard" } br;
                                                    small { "Built a custom marketing platform used by thousands of small businesses to access premium content and the tools they need to grow their businesses." }
                                                }
                                                div.timeline-item {
                                                    span.timeline-dot {}
                                                    span.timeline-year { "2013 — 12 Years" }
                                                    span.active-project { " (Active Project)" } br;
                                                    strong { "Technical Investment in ACRM" } br;
                                                    small { "Engineered and implemented foundational modules used across many builds that all AppCove clients rely on today." }
                                                }
                                                div.timeline-item {
                                                    span.timeline-dot {}
                                                    span.timeline-year { "2016 — 9 Years" }
                                                    span.active-project { " (Active Project)" } br;
                                                    strong { "Tools for Financial Advisors" } br;
                                                    small { "Created custom software representing unique finance and investment products. Used by financial advisors to serve tens of thousands of clients." }
                                                }
                                                div.timeline-item {
                                                    span.timeline-dot {}
                                                    span.timeline-year { "2018 — 7 Years" }
                                                    span.active-project { " (Active Project)" } br;
                                                    strong { "Technical Investment in ACE" } br;
                                                    small { "Architected the AppCove Cloud Engine to consolidate cloud management, security, BDR, and deployment of custom software." }
                                                }
                                                div.timeline-item {
                                                    span.timeline-dot {}
                                                    span.timeline-year { "2020 — 5 Years" }
                                                    span.active-project { " (Active Project)" } br;
                                                    strong { "Virtual Event Platform" } br;
                                                    small { "Deployed an advanced virtual event platform used by dozens of organizations and hundreds of virtual or hybrid events." }
                                                }
                                                div.timeline-item {
                                                    span.timeline-dot {}
                                                    span.timeline-year { "2023" }
                                                    span.active-project { " (Active Project)" } br;
                                                    strong { "Technical Investment in ACE 2.0" } br;
                                                    small { "ACE 2.0 delivers stable infrastructure to all custom applications to be built going forward." }
                                                }
                                                div.timeline-item {
                                                    span.timeline-dot {}
                                                    span.timeline-year { "2025" }
                                                    span.active-project { " (Active Project)" } br;
                                                    strong { "Technical Investment in approck" } br;
                                                    small { "Engineered the next generation toolbox of software infrastructure, dev/ops, and database management tools." }
                                                }
                                                div.timeline-item {
                                                    span.timeline-dot {}
                                                    span.timeline-year { "2025" }
                                                    span.active-project { " (Active Project)" } br;
                                                    strong { "Fin/Tech Products" } br;
                                                    small { "Combining credit reporting, insurance underwriting, debt payoff plans, and community tools to support financial professionals." }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                });

        Response::HTML(doc.into())
    }
}
