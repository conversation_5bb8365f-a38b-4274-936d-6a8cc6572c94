            /* Site-wide background styling */
            body {
                background: linear-gradient(135deg, #e8f4f8 0%, #d1e7dd 50%, #f8f9fa 100%);
                background-attachment: fixed;
                min-height: 100vh;
            }

            about-appcove {
                /* Paper container styling */
                position: relative;
                background: #ffffff;
                background-image:
                    radial-gradient(circle at 20% 50%, transparent 20%, rgba(255,255,255,0.3) 21%, rgba(255,255,255,0.3) 34%, transparent 35%, transparent),
                    linear-gradient(0deg, rgba(0,0,0,0.1) 50%, transparent 50%);
                background-size: 75px 50px, 3px 3px;
                margin: 2rem auto;
                max-width: 1200px;
                padding: 3rem 2rem;
                border-radius: 8px;
                box-shadow:
                    0 8px 32px rgba(0, 0, 0, 0.12),
                    0 2px 8px rgba(0, 0, 0, 0.08),
                    inset 0 1px 0 rgba(255, 255, 255, 0.9);

                /* Shift content upward and to the left */
                transform: translateY(-1rem) translateX(-0.5rem);

                /* Ensure content stays within bounds */
                overflow: hidden;
                position: relative;
                z-index: 2; /* Ensure content appears above paper texture */
            }
